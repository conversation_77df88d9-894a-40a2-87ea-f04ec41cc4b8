#include <stdio.h>
#include <stdbool.h>
#include <locale.h>
#include <stdlib.h>
#include <math.h>
#include <time.h>
#include "../include/game.h"
#include "../include/board.h"
#include "../include/ai.h"
#include "../include/utils.h"
#include "../include/config.h"

// 全局变量定义
int T = -1; // 当前玩家，1表示上方，-1表示下方

// 初始化游戏
void initialize_game() {
    printf("=== Surakarta Chess ===\n");
    printf("Game Rules:\n");
    printf("1. 6x6 board, 12 pieces per side\n");
    printf("2. Pieces can move to adjacent empty positions\n");
    printf("3. Can capture along circular paths on board edges\n");
    printf("4. Win by reducing opponent to 1 or 0 pieces\n\n");
}

// 开始游戏
void start_game() {
    initialize_game();
    
    // 初始化棋盘
    int board[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    int player_side = choose_side();
    int first_player = choose_first_player();
    
    // T已经在main.cpp中根据BBQ设置了
    // 这里不需要额外设置
    
    printf("Game Start!\n");
    print_board(board);
    
    game_loop(board, player_side);
}

// 游戏主循环声明
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side);

// 游戏主循环
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side) {
    while (!is_game_over(board)) {
        display_game_status(board);
        
        if (T == player_side) {
            // 人类玩家回合
            int from_x, from_y, to_x, to_y;
            if (get_player_move(board, &from_x, &from_y, &to_x, &to_y)) {
                make_move(from_x, from_y, to_x, to_y, board);
                print_board(board);
            }
        } else {
            // AI turn
            printf("AI is thinking...\n");
            zong(board);
            print_board(board);
        }
        
        T = -T; // 切换玩家
    }
    
    // 游戏结束，显示结果
    int player1_count = count_pieces(board, PLAYER1);
    int player2_count = count_pieces(board, PLAYER2);
    
    if (player1_count > player2_count) {
        printf("Game Over! Upper player (+) wins!\n");
    } else {
        printf("Game Over! Lower player (-) wins!\n");
    }
}

// 获取玩家移动
bool get_player_move(int board[BOARD_SIZE][BOARD_SIZE], int* from_x, int* from_y, int* to_x, int* to_y) {
    printf("Enter piece position (row,col): ");
    if (scanf("%d,%d", from_x, from_y) != 2) {
        printf("Invalid input format!\n");
        return false;
    }

    printf("Enter target position (row,col): ");
    if (scanf("%d,%d", to_x, to_y) != 2) {
        printf("Invalid input format!\n");
        return false;
    }
    
    // 简单的有效性检查
    if (!is_valid_position(*from_x - 1, *from_y - 1) || 
        !is_valid_position(*to_x - 1, *to_y - 1)) {
        printf("Position out of board range!\n");
        return false;
    }

    if (board[*from_x - 1][*from_y - 1] != T) {
        printf("This is not your piece!\n");
        return false;
    }

    if (!is_empty(board, *to_x - 1, *to_y - 1)) {
        printf("Target position is not empty!\n");
        return false;
    }
    
    return true;
}

// 切换玩家
void switch_player() {
    T = -T;
}

// 显示游戏状态
void display_game_status(int board[BOARD_SIZE][BOARD_SIZE]) {
    int player1_count = count_pieces(board, PLAYER1);
    int player2_count = count_pieces(board, PLAYER2);
    
    printf("Current status: Upper(+): %d pieces, Lower(-): %d pieces\n",
           player1_count, player2_count);

    if (T == PLAYER1) {
        printf("Upper player's turn (+)\n");
    } else {
        printf("Lower player's turn (-)\n");
    }
}

// 选择阵营
int choose_side() {
    int choice;
    printf("Choose your side:\n");
    printf("1. Upper (+)\n");
    printf("2. Lower (-)\n");
    printf("Enter choice (1 or 2): ");
    scanf("%d", &choice);
    
    return (choice == 1) ? PLAYER1 : PLAYER2;
}

// 选择先手
int choose_first_player() {
    int choice;
    printf("Choose first player:\n");
    printf("1. Human first\n");
    printf("2. Computer first\n");
    printf("Enter choice (1 or 2): ");
    scanf("%d", &choice);
    
    return choice;
}

// 实现原始test.cpp中的关键函数
void print(int a[BOARD_SIZE][BOARD_SIZE]) {
    printf("\n  ");
    for (int j = 0; j < BOARD_SIZE; j++) {
        printf("%d ", j + 1);
    }
    printf("\n");

    for (int i = 0; i < BOARD_SIZE; i++) {
        printf("%d ", i + 1);
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (a[i][j] == 1) {
                printf("+ ");
            } else if (a[i][j] == -1) {
                printf("- ");
            } else {
                printf(". ");
            }
        }
        printf("\n");
    }
    printf("\n");
}

void zouchizi(int x, int y, int c, int d, int a[BOARD_SIZE][BOARD_SIZE]) {
    a[c-1][d-1] = a[x-1][y-1];
    a[x-1][y-1] = 0;
}

void zhuanhuan(int a[BOARD_SIZE][BOARD_SIZE], int b[BOARD_SIZE][BOARD_SIZE]) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            b[i][j] = a[i][j];
        }
    }
}

void zong(int a[BOARD_SIZE][BOARD_SIZE]) {
    // AI决策函数，智能选择最优移动
    extern int T;

    printf("AI is thinking... (AI controls %s pieces)\n", T == 1 ? "+" : "-");

    // 存储所有有效移动
    struct Move {
        int from_x, from_y, to_x, to_y;
        int score;
    };

    Move valid_moves[200];  // 足够存储所有可能的移动
    int move_count = 0;

    // 第一步：收集所有有效移动（静默检查，不打印错误）
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (a[i][j] == T) {  // 只考虑AI自己的棋子
                // 尝试8个方向的相邻移动
                int directions[8][2] = {{-1,-1}, {-1,0}, {-1,1}, {0,-1}, {0,1}, {1,-1}, {1,0}, {1,1}};

                for (int d = 0; d < 8; d++) {
                    int ni = i + directions[d][0];
                    int nj = j + directions[d][1];

                    // 静默检查移动有效性（不打印错误）
                    if (is_move_valid_silent(a, i, j, ni, nj, T)) {
                        // 评估这个移动
                        int score = evaluate_move(a, i, j, ni, nj, T);

                        // 存储有效移动
                        valid_moves[move_count].from_x = i + 1;
                        valid_moves[move_count].from_y = j + 1;
                        valid_moves[move_count].to_x = ni + 1;
                        valid_moves[move_count].to_y = nj + 1;
                        valid_moves[move_count].score = score;
                        move_count++;
                    }
                }
            }
        }
    }

    // 第二步：从有效移动中选择最佳移动
    if (move_count > 0) {
        // 找到最高分的移动
        int best_index = 0;
        for (int i = 1; i < move_count; i++) {
            if (valid_moves[i].score > valid_moves[best_index].score) {
                best_index = i;
            }
        }

        // 执行最佳移动
        Move best_move = valid_moves[best_index];
        printf("AI moves from (%d,%d) to (%d,%d) [Score: %d]\n",
               best_move.from_x, best_move.from_y, best_move.to_x, best_move.to_y, best_move.score);
        zouchizi(best_move.from_x, best_move.from_y, best_move.to_x, best_move.to_y, a);
    } else {
        printf("AI cannot find any valid move!\n");
    }
}

// 苏拉卡尔塔棋规则检查函数实现
bool is_valid_move(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player) {
    // 转换为0基索引
    from_x--; from_y--; to_x--; to_y--;

    // 1. 检查坐标是否在棋盘范围内
    if (from_x < 0 || from_x >= BOARD_SIZE || from_y < 0 || from_y >= BOARD_SIZE ||
        to_x < 0 || to_x >= BOARD_SIZE || to_y < 0 || to_y >= BOARD_SIZE) {
        printf("Error: Position out of board range!\n");
        return false;
    }

    // 2. 检查起始位置是否有己方棋子
    if (board[from_x][from_y] != player) {
        printf("Error: No piece of yours at position (%d,%d)!\n", from_x+1, from_y+1);
        return false;
    }

    // 3. 检查目标位置
    if (board[to_x][to_y] == player) {
        printf("Error: Cannot move to a position occupied by your own piece!\n");
        return false;
    }

    // 4. 如果目标位置为空，检查是否为相邻移动
    if (board[to_x][to_y] == 0) {
        if (is_adjacent_move(from_x, from_y, to_x, to_y)) {
            return true;
        } else {
            printf("Error: Can only move to adjacent empty positions!\n");
            return false;
        }
    }

    // 5. 如果目标位置有对方棋子，检查是否可以通过弧线吃子
    if (board[to_x][to_y] == -player) {
        if (can_capture_along_arc(board, from_x, from_y, to_x, to_y, player)) {
            return true;
        } else {
            printf("Error: Cannot capture - no valid arc path!\n");
            return false;
        }
    }

    return false;
}

bool is_adjacent_move(int from_x, int from_y, int to_x, int to_y) {
    int dx = abs(to_x - from_x);
    int dy = abs(to_y - from_y);

    // 相邻位置：8个方向，距离为1
    return (dx <= 1 && dy <= 1 && (dx + dy > 0));
}

bool can_capture_along_arc(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player) {
    // 苏拉卡尔塔棋的弧线路径检查
    // 这里实现简化版本：检查是否可以通过边缘弧线到达目标

    // 检查四个弧线方向
    if (check_arc_path(board, from_x, from_y, to_x, to_y, player, 1) ||
        check_arc_path(board, from_x, from_y, to_x, to_y, player, 2) ||
        check_arc_path(board, from_x, from_y, to_x, to_y, player, 3) ||
        check_arc_path(board, from_x, from_y, to_x, to_y, player, 4)) {
        return true;
    }

    return false;
}

bool check_arc_path(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player, int arc_direction) {
    // 简化的弧线路径检查
    // 实际的苏拉卡尔塔棋弧线路径比较复杂，这里实现基本逻辑

    // 检查起点和终点是否在棋盘边缘
    bool from_on_edge = (from_x == 0 || from_x == BOARD_SIZE-1 || from_y == 0 || from_y == BOARD_SIZE-1);
    bool to_on_edge = (to_x == 0 || to_x == BOARD_SIZE-1 || to_y == 0 || to_y == BOARD_SIZE-1);

    if (!from_on_edge || !to_on_edge) {
        return false;
    }

    // 简化检查：如果两点都在边缘且不相邻，认为可以通过弧线连接
    int dx = abs(to_x - from_x);
    int dy = abs(to_y - from_y);

    return (dx > 1 || dy > 1);
}

// 游戏结束检查
bool is_game_over(int board[BOARD_SIZE][BOARD_SIZE]) {
    int player1_count = count_pieces(board, 1);
    int player2_count = count_pieces(board, -1);

    // 规则5：当一方棋子全部被吃掉时棋局结束
    if (player1_count == 0 || player2_count == 0) {
        if (player1_count == 0) {
            printf("Game Over! Lower player (-) wins! Upper player has no pieces left.\n");
        } else {
            printf("Game Over! Upper player (+) wins! Lower player has no pieces left.\n");
        }
        return true;
    }

    // 规则6：检查是否还能吃子（简化实现）
    // 这里可以添加更复杂的逻辑来检查是否还有可能的吃子移动

    return false;
}

// 统计某个玩家的棋子数量
int count_pieces(int board[BOARD_SIZE][BOARD_SIZE], int player) {
    int count = 0;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == player) {
                count++;
            }
        }
    }
    return count;
}

// 静默检查移动有效性（不打印错误信息）
bool is_move_valid_silent(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player) {
    // 检查坐标是否在棋盘范围内
    if (to_x < 0 || to_x >= BOARD_SIZE || to_y < 0 || to_y >= BOARD_SIZE) {
        return false;
    }

    // 检查目标位置
    if (board[to_x][to_y] == player) {
        return false;  // 不能移动到己方棋子位置
    }

    // 如果目标位置为空，检查是否为相邻移动
    if (board[to_x][to_y] == 0) {
        return is_adjacent_move(from_x, from_y, to_x, to_y);
    }

    // 如果目标位置有对方棋子，检查是否可以通过弧线吃子
    if (board[to_x][to_y] == -player) {
        return can_capture_along_arc(board, from_x, from_y, to_x, to_y, player);
    }

    return false;
}

// 评估移动的分数
int evaluate_move(int board[BOARD_SIZE][BOARD_SIZE], int from_x, int from_y, int to_x, int to_y, int player) {
    int score = 0;

    // 1. 吃子奖励（最高优先级）
    if (board[to_x][to_y] == -player) {
        score += 1000;  // 吃子是最重要的
    }

    // 2. 位置价值
    // 中心位置更有价值
    int center_distance = abs(to_x - 2.5) + abs(to_y - 2.5);
    score += (10 - center_distance) * 5;

    // 3. 前进奖励
    if (player == 1) {  // 上方玩家向下前进
        score += (to_x - from_x) * 10;
    } else {  // 下方玩家向上前进
        score += (from_x - to_x) * 10;
    }

    // 4. 靠近对方棋子的奖励
    int min_enemy_distance = 999;
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == -player) {
                int dist = abs(to_x - i) + abs(to_y - j);
                if (dist < min_enemy_distance) {
                    min_enemy_distance = dist;
                }
            }
        }
    }
    score += (10 - min_enemy_distance) * 3;

    // 5. 避免边缘位置（除非是为了吃子）
    if (board[to_x][to_y] != -player) {
        if (to_x == 0 || to_x == BOARD_SIZE-1 || to_y == 0 || to_y == BOARD_SIZE-1) {
            score -= 5;
        }
    }

    // 6. 随机因子，避免AI过于机械
    score += rand() % 5;

    return score;
}
