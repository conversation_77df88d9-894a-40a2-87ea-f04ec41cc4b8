#include <stdio.h>
#include <stdbool.h>
#include <locale.h>
#include "../include/game.h"
#include "../include/board.h"
#include "../include/ai.h"
#include "../include/utils.h"
#include "../include/config.h"

// 全局变量定义
int current_player = PLAYER1;

// 初始化游戏
void initialize_game() {
    printf("=== Surakarta Chess ===\n");
    printf("Game Rules:\n");
    printf("1. 6x6 board, 12 pieces per side\n");
    printf("2. Pieces can move to adjacent empty positions\n");
    printf("3. Can capture along circular paths on board edges\n");
    printf("4. Win by reducing opponent to 1 or 0 pieces\n\n");
}

// 开始游戏
void start_game() {
    initialize_game();
    
    // 初始化棋盘
    int board[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    int player_side = choose_side();
    int first_player = choose_first_player();
    
    if (player_side == PLAYER2) {
        current_player = PLAYER2;
    } else {
        current_player = PLAYER1;
    }
    
    if (first_player == 2) {
        current_player = -current_player;
    }
    
    printf("Game Start!\n");
    print_board(board);
    
    game_loop(board, player_side);
}

// 游戏主循环声明
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side);

// 游戏主循环
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side) {
    while (!is_game_over(board)) {
        display_game_status(board);
        
        if (current_player == player_side) {
            // 人类玩家回合
            int from_x, from_y, to_x, to_y;
            if (get_player_move(board, &from_x, &from_y, &to_x, &to_y)) {
                make_move(from_x, from_y, to_x, to_y, board);
                print_board(board);
            }
        } else {
            // AI turn
            printf("AI is thinking...\n");
            make_ai_move(board, current_player);
            print_board(board);
        }
        
        switch_player();
    }
    
    // 游戏结束，显示结果
    int player1_count = count_pieces(board, PLAYER1);
    int player2_count = count_pieces(board, PLAYER2);
    
    if (player1_count > player2_count) {
        printf("Game Over! Upper player (+) wins!\n");
    } else {
        printf("Game Over! Lower player (-) wins!\n");
    }
}

// 获取玩家移动
bool get_player_move(int board[BOARD_SIZE][BOARD_SIZE], int* from_x, int* from_y, int* to_x, int* to_y) {
    printf("Enter piece position (row,col): ");
    if (scanf("%d,%d", from_x, from_y) != 2) {
        printf("Invalid input format!\n");
        return false;
    }

    printf("Enter target position (row,col): ");
    if (scanf("%d,%d", to_x, to_y) != 2) {
        printf("Invalid input format!\n");
        return false;
    }
    
    // 简单的有效性检查
    if (!is_valid_position(*from_x - 1, *from_y - 1) || 
        !is_valid_position(*to_x - 1, *to_y - 1)) {
        printf("Position out of board range!\n");
        return false;
    }

    if (board[*from_x - 1][*from_y - 1] != current_player) {
        printf("This is not your piece!\n");
        return false;
    }

    if (!is_empty(board, *to_x - 1, *to_y - 1)) {
        printf("Target position is not empty!\n");
        return false;
    }
    
    return true;
}

// 切换玩家
void switch_player() {
    current_player = -current_player;
}

// 显示游戏状态
void display_game_status(int board[BOARD_SIZE][BOARD_SIZE]) {
    int player1_count = count_pieces(board, PLAYER1);
    int player2_count = count_pieces(board, PLAYER2);
    
    printf("Current status: Upper(+): %d pieces, Lower(-): %d pieces\n",
           player1_count, player2_count);

    if (current_player == PLAYER1) {
        printf("Upper player's turn (+)\n");
    } else {
        printf("Lower player's turn (-)\n");
    }
}

// 选择阵营
int choose_side() {
    int choice;
    printf("Choose your side:\n");
    printf("1. Upper (+)\n");
    printf("2. Lower (-)\n");
    printf("Enter choice (1 or 2): ");
    scanf("%d", &choice);
    
    return (choice == 1) ? PLAYER1 : PLAYER2;
}

// 选择先手
int choose_first_player() {
    int choice;
    printf("Choose first player:\n");
    printf("1. Human first\n");
    printf("2. Computer first\n");
    printf("Enter choice (1 or 2): ");
    scanf("%d", &choice);
    
    return choice;
}
