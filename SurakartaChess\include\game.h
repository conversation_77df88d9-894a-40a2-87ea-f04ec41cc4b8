#ifndef GAME_H
#define GAME_H

#include <stdbool.h>
#include "config.h"

// 全局变量声明
extern int current_player;

// 游戏主要功能
void initialize_game();
void start_game();
void game_loop(int board[BOARD_SIZE][BOARD_SIZE], int player_side);
bool get_player_move(int board[BOARD_SIZE][BOARD_SIZE], int* from_x, int* from_y, int* to_x, int* to_y);
void switch_player();
void display_game_status(int board[BOARD_SIZE][BOARD_SIZE]);

// 游戏设置
int choose_side();
int choose_first_player();

#endif // GAME_H
