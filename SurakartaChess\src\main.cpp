#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "../include/game.h"
#include "../include/config.h"

extern int T;

int main() {
    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 基于原始test.cpp的主函数逻辑
    int b[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };

    int BBQ, s;
    int x, y, c, d, i;

    print(b);
    printf("Enter 1 for upper side, 2 for lower side: ");
    scanf("%d", &BBQ);
    printf("  * Game Start *\n");
    printf("  1 Human first\n");
    printf("  2 Computer first\n");

    if (BBQ == 1) {
        T = 1;
        printf("Enter 1 or 2 to choose first player:\n");
        scanf("%d", &s);

        if (s == 1) {
            // 人类先手
            printf("Enter piece position (row,col):\n");
            scanf("%d,%d", &x, &y);
            printf("Enter target position (row,col):\n");
            scanf("%d,%d", &c, &d);
            zouchizi(x, y, c, d, b);
            print(b);

            for (i = 0; i < 100; i++) {
                zong(b);  // AI移动
                print(b);
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                zouchizi(x, y, c, d, b);
                print(b);
            }
        } else if (s == 2) {
            // AI先手
            zong(b);
            print(b);

            for (i = 0; i < 100; i++) {
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                zouchizi(x, y, c, d, b);
                print(b);
                zong(b);
                print(b);
            }
        }
    } else if (BBQ == 2) {
        T = -1;
        printf("Enter 1 or 2 to choose first player:\n");
        scanf("%d", &s);

        if (s == 1) {
            // 人类先手
            printf("Enter piece position (row,col):\n");
            scanf("%d,%d", &x, &y);
            printf("Enter target position (row,col):\n");
            scanf("%d,%d", &c, &d);
            zouchizi(x, y, c, d, b);
            print(b);

            for (i = 0; i < 100; i++) {
                zong(b);
                print(b);
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                zouchizi(x, y, c, d, b);
                print(b);
            }
        } else if (s == 2) {
            // AI先手
            zong(b);
            print(b);

            for (i = 0; i < 100; i++) {
                printf("Enter piece position (row,col):\n");
                scanf("%d,%d", &x, &y);
                printf("Enter target position (row,col):\n");
                scanf("%d,%d", &c, &d);
                zouchizi(x, y, c, d, b);
                print(b);
                zong(b);
                print(b);
            }
        }
    }

    return 0;
}
