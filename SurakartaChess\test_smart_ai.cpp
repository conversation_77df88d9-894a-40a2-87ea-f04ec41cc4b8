#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include "include/config.h"
#include "include/game.h"

extern int T;

int main() {
    srand(time(NULL));
    
    printf("=== Smart AI Test ===\n");
    
    // 测试场景1：初始状态
    int board1[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    printf("Test 1: Initial position - AI as lower player (-)\n");
    print(board1);
    T = -1;
    zong(board1);
    print(board1);
    
    // 测试场景2：有吃子机会
    int board2[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 1, 0, 0, 0},
        {0, 0, -1, 0, 0, 0},
        {-1, -1, 0, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    printf("\nTest 2: Capture opportunity - AI as lower player (-)\n");
    printf("AI should capture the upper piece at (3,3)\n");
    print(board2);
    T = -1;
    zong(board2);
    print(board2);
    
    // 测试场景3：AI作为上方玩家
    int board3[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    printf("\nTest 3: AI as upper player (+)\n");
    print(board3);
    T = 1;
    zong(board3);
    print(board3);
    
    printf("\nSmart AI tests completed!\n");
    return 0;
}
