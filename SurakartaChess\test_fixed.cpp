#include <stdio.h>
#include <locale.h>
#ifdef _WIN32
#include <windows.h>
#endif
#include "include/config.h"
#include "include/board.h"

int main() {
#ifdef _WIN32
    // 设置Windows控制台支持UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif
    setlocale(LC_ALL, "");
    
    printf("=== Surakarta Chess Test ===\n");
    
    // 初始化棋盘
    int board[BOARD_SIZE][BOARD_SIZE] = {
        {1, 1, 1, 1, 1, 1},
        {1, 1, 1, 1, 1, 1},
        {0, 0, 0, 0, 0, 0},
        {0, 0, 0, 0, 0, 0},
        {-1, -1, -1, -1, -1, -1},
        {-1, -1, -1, -1, -1, -1}
    };
    
    printf("Initial board:\n");
    print_board(board);
    
    // 测试移动
    printf("Testing move from (1,1) to (3,1):\n");
    make_move(1, 1, 3, 1, board);
    print_board(board);
    
    // 测试吃子检查
    printf("Testing capture check at position (0,0):\n");
    int capture_result = check_capture_direction1(board, 0, 0);
    printf("Capture result: %d\n", capture_result);
    
    printf("Test completed successfully!\n");
    printf("Press Enter to exit...\n");
    getchar();
    
    return 0;
}
