#include <stdio.h>
#include <stdbool.h>
#include "../include/board.h"
#include "../include/config.h"

// 打印棋盘
void print_board(int board[BOARD_SIZE][BOARD_SIZE]) {
    printf("\n  ");
    for (int j = 0; j < BOARD_SIZE; j++) {
        printf("%d ", j + 1);
    }
    printf("\n");
    
    for (int i = 0; i < BOARD_SIZE; i++) {
        printf("%d ", i + 1);
        for (int j = 0; j < BOARD_SIZE; j++) {
            if (board[i][j] == PLAYER1) {
                printf("● ");
            } else if (board[i][j] == PLAYER2) {
                printf("○ ");
            } else {
                printf("· ");
            }
        }
        printf("\n");
    }
    printf("\n");
}

// 复制棋盘
void copy_board(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]) {
    for (int i = 0; i < BOARD_SIZE; i++) {
        for (int j = 0; j < BOARD_SIZE; j++) {
            dest[i][j] = src[i][j];
        }
    }
}

// 执行移动
void make_move(int x, int y, int new_x, int new_y, int board[BOARD_SIZE][BOARD_SIZE]) {
    board[new_x - 1][new_y - 1] = board[x - 1][y - 1];
    board[x - 1][y - 1] = EMPTY;
}

// 检查位置是否有效
bool is_valid_position(int x, int y) {
    return x >= 0 && x < BOARD_SIZE && y >= 0 && y < BOARD_SIZE;
}

// 检查位置是否为空
bool is_empty(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    return is_valid_position(x, y) && board[x][y] == EMPTY;
}

// 苏拉卡尔塔棋的特殊吃子规则实现
// 这些函数实现了沿着棋盘边缘的环形路径进行吃子的逻辑

// 检查左上角环形路径吃子
int check_capture_direction1(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    int player = board[x][y];
    if (player == EMPTY) return 0;

    // 左上角环形路径：上边->左边->下边->右边
    // 路径坐标序列
    int path[][2] = {
        {0,0}, {0,1}, {0,2}, {0,3}, {0,4}, {0,5},  // 上边
        {1,5}, {2,5}, {3,5}, {4,5}, {5,5},        // 右边
        {5,4}, {5,3}, {5,2}, {5,1}, {5,0},        // 下边
        {4,0}, {3,0}, {2,0}, {1,0}                // 左边
    };

    int path_len = sizeof(path) / sizeof(path[0]);
    int start_pos = -1;

    // 找到当前棋子在路径中的位置
    for (int i = 0; i < path_len; i++) {
        if (path[i][0] == x && path[i][1] == y) {
            start_pos = i;
            break;
        }
    }

    if (start_pos == -1) return 0; // 不在环形路径上

    // 沿路径检查是否可以吃子
    for (int i = 1; i < path_len; i++) {
        int next_pos = (start_pos + i) % path_len;
        int nx = path[next_pos][0];
        int ny = path[next_pos][1];

        if (board[nx][ny] == -player) {
            // 找到敌方棋子，检查路径是否畅通
            bool path_clear = true;
            for (int j = 1; j < i; j++) {
                int check_pos = (start_pos + j) % path_len;
                int cx = path[check_pos][0];
                int cy = path[check_pos][1];
                if (board[cx][cy] != EMPTY) {
                    path_clear = false;
                    break;
                }
            }
            if (path_clear) return CAPTURE_BONUS;
        } else if (board[nx][ny] != EMPTY) {
            break; // 路径被阻挡
        }
    }

    return 0;
}

// 检查右上角环形路径吃子
int check_capture_direction2(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    int player = board[x][y];
    if (player == EMPTY) return 0;

    // 右上角环形路径：右边->上边->左边->下边
    int path[][2] = {
        {0,5}, {0,4}, {0,3}, {0,2}, {0,1}, {0,0},  // 上边（右到左）
        {1,0}, {2,0}, {3,0}, {4,0}, {5,0},        // 左边
        {5,1}, {5,2}, {5,3}, {5,4}, {5,5},        // 下边
        {4,5}, {3,5}, {2,5}, {1,5}                // 右边
    };

    return check_circular_capture(board, x, y, path, sizeof(path) / sizeof(path[0]));
}

// 检查左下角环形路径吃子
int check_capture_direction3(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    int player = board[x][y];
    if (player == EMPTY) return 0;

    // 左下角环形路径：下边->左边->上边->右边
    int path[][2] = {
        {5,0}, {5,1}, {5,2}, {5,3}, {5,4}, {5,5},  // 下边
        {4,5}, {3,5}, {2,5}, {1,5}, {0,5},        // 右边
        {0,4}, {0,3}, {0,2}, {0,1}, {0,0},        // 上边
        {1,0}, {2,0}, {3,0}, {4,0}                // 左边
    };

    return check_circular_capture(board, x, y, path, sizeof(path) / sizeof(path[0]));
}

// 检查右下角环形路径吃子
int check_capture_direction4(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    int player = board[x][y];
    if (player == EMPTY) return 0;

    // 右下角环形路径：右边->下边->左边->上边
    int path[][2] = {
        {1,5}, {2,5}, {3,5}, {4,5}, {5,5},        // 右边
        {5,4}, {5,3}, {5,2}, {5,1}, {5,0},        // 下边（右到左）
        {4,0}, {3,0}, {2,0}, {1,0}, {0,0},        // 左边
        {0,1}, {0,2}, {0,3}, {0,4}, {0,5}         // 上边
    };

    return check_circular_capture(board, x, y, path, sizeof(path) / sizeof(path[0]));
}

// 通用的环形路径吃子检查函数
int check_circular_capture(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int path[][2], int path_len) {
    int player = board[x][y];
    if (player == EMPTY) return 0;

    int start_pos = -1;

    // 找到当前棋子在路径中的位置
    for (int i = 0; i < path_len; i++) {
        if (path[i][0] == x && path[i][1] == y) {
            start_pos = i;
            break;
        }
    }

    if (start_pos == -1) return 0; // 不在环形路径上

    // 沿路径检查是否可以吃子
    for (int i = 1; i < path_len; i++) {
        int next_pos = (start_pos + i) % path_len;
        int nx = path[next_pos][0];
        int ny = path[next_pos][1];

        if (board[nx][ny] == -player) {
            // 找到敌方棋子，检查路径是否畅通
            bool path_clear = true;
            for (int j = 1; j < i; j++) {
                int check_pos = (start_pos + j) % path_len;
                int cx = path[check_pos][0];
                int cy = path[check_pos][1];
                if (board[cx][cy] != EMPTY) {
                    path_clear = false;
                    break;
                }
            }
            if (path_clear) return CAPTURE_BONUS;
        } else if (board[nx][ny] != EMPTY) {
            break; // 路径被阻挡
        }
    }

    return 0;
}

void execute_capture1(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    execute_circular_capture(board, x, y, 1);
}

void execute_capture2(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    execute_circular_capture(board, x, y, 2);
}

void execute_capture3(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    execute_circular_capture(board, x, y, 3);
}

void execute_capture4(int board[BOARD_SIZE][BOARD_SIZE], int x, int y) {
    execute_circular_capture(board, x, y, 4);
}

// 通用的执行环形吃子函数
void execute_circular_capture(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int direction) {
    int player = board[x][y];
    if (player == EMPTY) return;

    int path[20][2];
    int path_len = 0;

    // 根据方向设置路径
    switch (direction) {
        case 1: // 左上角环形路径
            {
                int temp_path[][2] = {
                    {0,0}, {0,1}, {0,2}, {0,3}, {0,4}, {0,5},
                    {1,5}, {2,5}, {3,5}, {4,5}, {5,5},
                    {5,4}, {5,3}, {5,2}, {5,1}, {5,0},
                    {4,0}, {3,0}, {2,0}, {1,0}
                };
                path_len = sizeof(temp_path) / sizeof(temp_path[0]);
                for (int i = 0; i < path_len; i++) {
                    path[i][0] = temp_path[i][0];
                    path[i][1] = temp_path[i][1];
                }
            }
            break;
        case 2: // 右上角环形路径
            {
                int temp_path[][2] = {
                    {0,5}, {0,4}, {0,3}, {0,2}, {0,1}, {0,0},
                    {1,0}, {2,0}, {3,0}, {4,0}, {5,0},
                    {5,1}, {5,2}, {5,3}, {5,4}, {5,5},
                    {4,5}, {3,5}, {2,5}, {1,5}
                };
                path_len = sizeof(temp_path) / sizeof(temp_path[0]);
                for (int i = 0; i < path_len; i++) {
                    path[i][0] = temp_path[i][0];
                    path[i][1] = temp_path[i][1];
                }
            }
            break;
        case 3: // 左下角环形路径
            {
                int temp_path[][2] = {
                    {5,0}, {5,1}, {5,2}, {5,3}, {5,4}, {5,5},
                    {4,5}, {3,5}, {2,5}, {1,5}, {0,5},
                    {0,4}, {0,3}, {0,2}, {0,1}, {0,0},
                    {1,0}, {2,0}, {3,0}, {4,0}
                };
                path_len = sizeof(temp_path) / sizeof(temp_path[0]);
                for (int i = 0; i < path_len; i++) {
                    path[i][0] = temp_path[i][0];
                    path[i][1] = temp_path[i][1];
                }
            }
            break;
        case 4: // 右下角环形路径
            {
                int temp_path[][2] = {
                    {1,5}, {2,5}, {3,5}, {4,5}, {5,5},
                    {5,4}, {5,3}, {5,2}, {5,1}, {5,0},
                    {4,0}, {3,0}, {2,0}, {1,0}, {0,0},
                    {0,1}, {0,2}, {0,3}, {0,4}, {0,5}
                };
                path_len = sizeof(temp_path) / sizeof(temp_path[0]);
                for (int i = 0; i < path_len; i++) {
                    path[i][0] = temp_path[i][0];
                    path[i][1] = temp_path[i][1];
                }
            }
            break;
        default:
            return;
    }

    int start_pos = -1;

    // 找到当前棋子在路径中的位置
    for (int i = 0; i < path_len; i++) {
        if (path[i][0] == x && path[i][1] == y) {
            start_pos = i;
            break;
        }
    }

    if (start_pos == -1) return;

    // 沿路径寻找并吃掉敌方棋子
    for (int i = 1; i < path_len; i++) {
        int next_pos = (start_pos + i) % path_len;
        int nx = path[next_pos][0];
        int ny = path[next_pos][1];

        if (board[nx][ny] == -player) {
            // 找到敌方棋子，检查路径是否畅通
            bool path_clear = true;
            for (int j = 1; j < i; j++) {
                int check_pos = (start_pos + j) % path_len;
                int cx = path[check_pos][0];
                int cy = path[check_pos][1];
                if (board[cx][cy] != EMPTY) {
                    path_clear = false;
                    break;
                }
            }
            if (path_clear) {
                // 执行吃子：移动棋子并清除被吃的棋子
                board[nx][ny] = player;
                board[x][y] = EMPTY;
                return;
            }
        } else if (board[nx][ny] != EMPTY) {
            break; // 路径被阻挡
        }
    }
}
